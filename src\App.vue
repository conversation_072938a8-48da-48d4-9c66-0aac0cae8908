<script setup lang="ts">
import { RouterView, useRouter } from 'vue-router';
import { ref, onMounted, onUnmounted } from 'vue';
import ThumbNail from '@/components/ThumbNail.vue';
import { provide } from 'vue';
import PicIcon from '@/assets/image/common/pic_icon.jpg';
import TableIcon from '@/assets/image/common/table.png';
import CodeIcon from '@/assets/image/common/terminal.png';
import katex from 'katex';
import { bindKeyWordsHover } from '@/composables/useKeyWordsHover';
import { decodeHTML } from 'entities';
import { userInfoStore } from './stores/userInfo';

const userinfo = userInfoStore();

// 窗口多开处理逻辑
document.addEventListener('visibilitychange', () => {
  if (!document['hidden']) {
    userinfo.getUserInfo();
    // console.log('出现');
  } else {
    // console.log('隐藏');
  }
});
const imgs: Array<HTMLElement> = [];
const thumbNailElement = ref<HTMLElement | null>(null);
const observer = new MutationObserver((mutationsList) => {
  let scripts = Array.prototype.slice.call(document.body.getElementsByTagName('script'));
  scripts.forEach(function (script) {
    if (!script.type || !script.type.match(/math\/tex/i)) {
      return -1;
    }

    // 检查是否在 id=underline 元素内(可划词内容由划词包内容渲染），如果是则跳过渲染
    // let parentElement = script.parentElement;
    // while (parentElement) {
    //   if (parentElement.id && parentElement.id == 'underline') {
    //     return -1; // 跳过渲染
    //   }
    //   parentElement = parentElement.parentElement;
    // }

    // const stringIndex = script.getAttribute('stringIndex');
    const display = script.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;

    const katexElement = document.createElement(display ? 'div' : 'span');
    katexElement.setAttribute('class', display ? 'equation' : 'inline-equation');

    // 使用安全的entities库进行HTML实体解码,避免xss攻击
    const decodedText = decodeHTML(script.text);
    katexElement.setAttribute('latexCode', script.text);

    try {
      // 预处理公式文本，将 align 环境替换为 align* 以禁用自动编号
      let processedText = decodedText.replace(/\s+/g, ' ');
      processedText = processedText.replace(/\\begin\{align\}/g, '\\begin{align*}');
      processedText = processedText.replace(/\\end\{align\}/g, '\\end{align*}');

      const htmlString = katex.renderToString(processedText, {
        displayMode: display,
        throwOnError: false,
        output: 'html'
      });

      katexElement.innerHTML = htmlString;
    } catch (err) {
      //console.error(err); linter doesn't like this
      katexElement.textContent = decodedText;
    }
    script.parentNode.replaceChild(katexElement, script);
  });

  const images = document.querySelectorAll('img');

  images.forEach((img) => {
    if (imgs.includes(img)) {
      return;
    }
    img.addEventListener(
      'dblclick',
      function () {
        const range = document.createRange();
        range.selectNode(this);

        const selection = window.getSelection();
        selection!.removeAllRanges();
        selection!.addRange(range);
      },
      true
    );
    imgs.push(img);
  });
  const questionLists = document.querySelectorAll('.questionList, .questionPop, .quesCollapse');
  questionLists.forEach((questionList) => {
    const imgElements = questionList.querySelectorAll('img:not([thumbnail])');
    imgElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = PicIcon;
      newImg.style.height = '14px';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
    const tableElements = questionList.querySelectorAll('table:not([thumbnail])');
    tableElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = TableIcon;
      newImg.style.height = '14px';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
  });

  // 处理 keyWords 区域的悬浮放大功能
  const keyWordsContainers = document.querySelectorAll('.keyWords');
  keyWordsContainers.forEach((container) => {
    const elements = container.querySelectorAll('.equation, img');
    elements.forEach((element) => {
      bindKeyWordsHover(element as HTMLElement);
    });
  });
});
const scale = window.devicePixelRatio || 1;
if (scale >= 1.25) {
  document.body.classList.add('zoomed-in');
} else {
  document.body.classList.remove('zoomed-in');
}

const mediaQuery = window.matchMedia('(min-width: 1000px)');
mediaQuery.addEventListener('change', (e) => {
  if (e.matches) {
    console.log('屏幕宽度大于或等于 1000px');
  } else {
    console.log('屏幕宽度小于 1000px');
  }
});

onMounted(() => {
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
});

onUnmounted(() => {
  observer.disconnect();
  window.handleWord = null;
});
</script>
<template>
  <router-view></router-view>
  <ThumbNail v-model="thumbNailElement"></ThumbNail>
</template>
<style scoped></style>
