.wrapper {
  display: flex;
  flex-direction: column !important;
  width: 100%;
  font-size: 14px;
}
.wrapper .up-wrapper {
  width: 100%;
  display: flex;
  background-color: white;
  padding: 35px 30px;
  flex-direction: column;
  min-height: 500px;
}
.wrapper .up-wrapper .header {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.wrapper .up-wrapper .header .header-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.wrapper .up-wrapper .header .header-left .header-title {
  font-size: 24px;
  font-weight: 600;
  margin-right: 10px;
}
.wrapper .up-wrapper .header .header-left .header-title :deep(p) {
  margin: 0;
}
.wrapper .up-wrapper .header .header-left .header-type {
  white-space: nowrap;
  display: flex;
  height: 20px;
  padding: 0 5px;
  border: 1px solid var(--color-grey);
  color: var(--color-grey);
  border-radius: 3px;
  font-size: 14px;
  min-width: 80px;
  margin-right: 10px;
  bottom: 0;
}
.wrapper .up-wrapper .base-info {
  width: 100%;
  color: var(--color-deep);
  font-size: 12px;
  /* 保存时间样式 */
}
.wrapper .up-wrapper .base-info .author-info {
  margin-right: 40px;
}
.wrapper .up-wrapper .base-info .save-time {
  display: inline-block;
}
.wrapper .up-wrapper .detail-info {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  color: var(--color-black);
  font-size: 14px;
  position: relative;
}
.wrapper .up-wrapper .detail-info .detail-info-left {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.wrapper .up-wrapper .detail-info .detail-info-left .info-syn {
  width: 90%;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  flex-direction: row;
}
.wrapper .up-wrapper .detail-info .detail-info-left .info-syn .info-syn-text-block {
  width: 100%;
  white-space: normal;
  overflow-wrap: break-word;
}
.wrapper .up-wrapper .detail-info .detail-info-left .info-syn .info-syn-text-block .info-syn-text {
  font-weight: 400;
  white-space: normal;
}
.wrapper .up-wrapper .detail-info .detail-info-right {
  width: 82px;
  height: 20px;
  background-color: #d6e9f6;
  border-radius: 4px;
  position: absolute;
  bottom: 2px;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}
.wrapper .up-wrapper .main-container {
  margin-top: 19px;
}
.wrapper .up-wrapper .main-container .switch {
  display: flex;
  align-items: center;
  justify-content: end;
}
.wrapper .up-wrapper .main-container .switch .mode-hint {
  margin-left: 10px;
  font-size: 12px;
  color: var(--color-grey);
  font-style: italic;
}
.wrapper .up-wrapper .main-container .content-container {
  display: flex;
  width: 100%;
  flex-direction: column;
  word-break: break-all;
}
.wrapper .up-wrapper .main-container .proof-container {
  margin-top: 30px;
  font-size: 14px;
}
.wrapper .up-wrapper .main-container .proof-container .proof-container-title {
  color: var(--color-black);
  font-weight: 600;
  margin-bottom: 5px;
}
.wrapper .up-wrapper .main-container .proof-container .proof-block {
  background-color: var(--color-light);
  border-radius: 3px;
  padding: 10px;
  margin-bottom: 10px;
}
.wrapper .up-wrapper .main-container .proof-container .proof-block .proof-block-item {
  margin-top: 5px;
  display: flex;
  flex-direction: row;
}
.wrapper .up-wrapper .main-container .proof-container .proof-block .proof-block-item :deep(p) {
  margin: 0;
}
.wrapper .up-wrapper .main-container .proof-container .proof-block .proof-block-item .proof-item-label {
  font-weight: 600;
  margin-right: 20px;
}
.wrapper .down-wrapper {
  width: 100%;
  background-color: white;
  margin-top: 20px;
  padding: 30px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
}
.wrapper .down-wrapper .ref-container-title {
  color: var(--color-black);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
}
.wrapper .down-wrapper .ref-list {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.wrapper .down-wrapper .ref-list .ref-line {
  width: 100%;
  padding: 5px 0px;
  display: flex;
  flex-direction: row;
  font-size: 14px;
}
.wrapper .down-wrapper .ref-list .ref-line .ref-line-name {
  padding: 5px 10px;
  width: 70%;
  background-color: var(--color-light);
}
.wrapper .down-wrapper .ref-list .ref-line .ref-line-chapter {
  margin-left: 10px;
  padding: 5px 10px;
  width: 30%;
  background-color: var(--color-light);
}
.header-right {
  min-width: 70px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.header-right .detail-info-right-text {
  color: var(--color-primary);
  margin-left: 5px;
  font-size: 12px;
}
.header-right-text {
  white-space: nowrap;
  margin-left: 5px;
  color: var(--color-primary);
  font-size: 12px;
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.keyword-container {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  max-width: 320px;
}
/* 问号图标样式 */
.question-icon {
  cursor: pointer;
  position: relative;
  animation: questionIconFadeIn 0.2s ease-in-out;
}
.question-icon-circle {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.question-icon-circle img {
  width: 16px;
  height: 16px;
}
.question-icon:hover .question-icon-circle {
  background: #f2f2f2;
}
/* 悬浮提示样式 */
.question-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  background: #666666;
  color: white;
  padding: 2px 5px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}
.question-icon:hover .question-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
}
/* 浮动弹窗样式 */
.floating-content {
  padding: 8px 12px;
  background: var(--color-primary);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 100%;
  overflow: hidden;
  min-width: 150px;
  animation: fadeIn 0.2s ease-in-out;
}
.floating-content .floating-content-item {
  padding: 0;
  transition: all 0.2s ease;
  max-width: 400px;
}
.floating-content .floating-content-item:hover {
  font-weight: 700;
  cursor: pointer;
  transform: translateX(2px);
}
.floatingContainer {
  z-index: 10001;
}
/* 缩放动画 */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.2s ease;
}
.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
